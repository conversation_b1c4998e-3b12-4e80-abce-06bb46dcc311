import { loadEnvironment } from "../config/env-loader";
import { DBGiftsCollection } from "../firebase/firebase-admin";
import type {
  AppDate,
  GiftEntity,
  GiftStatus,
} from "../mikerudenko/marketplace-shared";
import { log } from "../utils/logger";

loadEnvironment();

export async function createGift(
  giftData: Omit<GiftEntity, "id" | "createdAt" | "updatedAt">,
  batch?: FirebaseFirestore.WriteBatch
) {
  try {
    const giftRef = DBGiftsCollection.doc();

    const giftEntity: GiftEntity = {
      id: giftRef.id,
      ...giftData,
      createdAt: new Date() as AppDate,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.set(giftRef, giftEntity);
    } else {
      await giftRef.set(giftEntity);
    }

    log.info("Gift created successfully", {
      operation: "create_gift",
      giftId: giftRef.id,
      ownerTgId: giftData.owner_tg_id,
      collectionId: giftData.collectionId,
    });

    return giftRef.id;
  } catch (error) {
    log.error("Failed to create gift", error, {
      operation: "create_gift",
      ownerTgId: giftData.owner_tg_id,
      collectionId: giftData.collectionId,
    });
    throw new Error(`Failed to create gift: ${error}`);
  }
}

export async function updateGiftOwnership(
  giftId: string,
  newOwnerTgId: string,
  status: GiftStatus,
  batch?: FirebaseFirestore.WriteBatch
) {
  try {
    const giftRef = DBGiftsCollection.doc(giftId);

    const updateData = {
      owner_tg_id: newOwnerTgId,
      status,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.update(giftRef, updateData);
    } else {
      await giftRef.update(updateData);
    }

    log.info("Gift ownership updated successfully", {
      operation: "update_gift_ownership",
      giftId,
      newOwnerTgId,
      status,
    });
  } catch (error) {
    log.error("Failed to update gift ownership", error, {
      operation: "update_gift_ownership",
      giftId,
      newOwnerTgId,
      status,
    });
    throw new Error(`Failed to update gift ownership: ${error}`);
  }
}
