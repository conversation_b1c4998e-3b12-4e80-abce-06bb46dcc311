import * as admin from "firebase-admin";
import {
  AppDate,
  BotSessionEntity,
} from "../../mikerudenko/marketplace-shared";
import { log } from "../../utils/logger";
import { DBBotSessionsCollection } from "../../firebase/firebase-admin";
import { validateUserId } from "../../firebase/validation-service";

export async function saveUserSessionByBot(params: {
  userId: string;
  botToken: string;
  sessionData: {
    echoMode?: boolean;
    withdrawal_gift_id?: string;
  };
}) {
  const { userId, sessionData } = params;

  try {
    validateUserId(userId);

    const sessionDoc = DBBotSessionsCollection.doc(userId);
    const existingSession = await sessionDoc.get();

    const sessionEntity: Partial<BotSessionEntity> = {
      id: userId,
      ...(sessionData.echoMode !== undefined && {
        echoMode: sessionData.echoMode,
      }),
      ...(sessionData.withdrawal_gift_id && {
        withdrawal_gift_id: sessionData.withdrawal_gift_id,
      }),
      createdAt: existingSession.exists
        ? (existingSession.data()?.createdAt as AppDate)
        : (new Date() as AppDate),
      updatedAt: new Date() as AppDate,
    };

    await sessionDoc.set(sessionEntity, { merge: true });

    log.info("Bot session saved successfully", {
      operation: "save_user_session_by_bot",
      userId,
      hasSessionData: !!sessionData,
    });

    return {
      success: true,
      message: "Session saved successfully",
    };
  } catch (error) {
    log.error("Failed to save bot session", error, {
      operation: "save_user_session_by_bot",
      userId,
    });
    throw error;
  }
}

export async function getUserSessionByBot(params: { userId: string }) {
  const { userId } = params;

  try {
    validateUserId(userId);

    const sessionDoc = await DBBotSessionsCollection.doc(userId).get();

    log.info("Bot session retrieved", {
      operation: "get_user_session_by_bot",
      userId,
      sessionExists: sessionDoc.exists,
    });

    if (!sessionDoc.exists) {
      return {
        success: true,
        session: null,
        message: "No session found",
      };
    }

    return {
      success: true,
      session: { id: sessionDoc.id, ...sessionDoc.data() } as BotSessionEntity,
      message: "Session retrieved successfully",
    };
  } catch (error) {
    log.error("Failed to get bot session", error, {
      operation: "get_user_session_by_bot",
      userId,
    });
    throw error;
  }
}

export async function clearUserSessionByBot(params: { userId: string }) {
  const { userId } = params;

  try {
    validateUserId(userId);

    // Only remove the withdrawal_gift_id field, keep other session data including language preferences
    await DBBotSessionsCollection.doc(userId).update({
      withdrawal_gift_id: admin.firestore.FieldValue.delete(),
      updatedAt: new Date() as AppDate,
    });

    log.info("Bot session withdrawal gift ID cleared successfully", {
      operation: "clear_user_session_by_bot",
      userId,
    });

    return {
      success: true,
      message: "Session withdrawal gift ID cleared successfully",
    };
  } catch (error) {
    log.error("Failed to clear bot session withdrawal gift ID", error, {
      operation: "clear_user_session_by_bot",
      userId,
    });
    throw error;
  }
}
