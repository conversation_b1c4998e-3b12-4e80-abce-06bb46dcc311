import { APP_CONFIG_DOC_ID } from "../miker<PERSON>nko/marketplace-shared";
import { log } from "../utils/logger";
import { DBAppConfigCollection } from "../firebase/firebase-admin";

export async function getAppConfig() {
  try {
    const doc = await DBAppConfigCollection.doc(APP_CONFIG_DOC_ID).get();

    if (!doc.exists) {
      log.warn("App config not found, returning default values", {
        operation: "get_app_config",
      });

      return {
        deposit_fee: 0,
        withdrawal_fee: 0,
        referrer_fee: 0,
        cancel_order_fee: 0,
        purchase_fee: 0,
        buyer_lock_percentage: 0,
        seller_lock_percentage: 0,
        resell_purchase_fee: 0,
        resell_purchase_fee_for_seller: 0,
        min_deposit_amount: 0,
        min_withdrawal_amount: 0,
        max_withdrawal_amount: 0,
        min_secondary_market_price: 0,
        fixed_cancel_order_fee: 0,
        cancel_price_proposal_fee: 0,
        lock_period: 21, // Default 21 days lock period
      };
    }

    const config = doc.data();

    log.info("App config retrieved successfully", {
      operation: "get_app_config",
      hasConfig: true,
    });

    return config;
  } catch (error) {
    log.error("Failed to get app config", error, {
      operation: "get_app_config",
    });
    throw error;
  }
}
