import { Context } from "telegraf";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { getGiftCollectionId } from "../utils/business-connection-helpers";
import { log } from "../utils/logger";
import { OrderGift } from "../miker<PERSON><PERSON>/marketplace-shared";
import { APP_NAME } from "../app.constants";
import { depositGiftDirectlyForBot } from "../services/gift-deposit-service";

export interface DirectDepositFlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
}

export const handleDirectGiftDeposit = async (
  flowContext: DirectDepositFlowContext,
  giftToTransfer: OrderGift
) => {
  const { ctx, chat_id, userId } = flowContext;

  log.info("Direct gift deposit flow started", {
    operation: "direct_gift_deposit_flow",
    chat_id,
    userId,
    gift: giftToTransfer,
  });

  try {
    // Get collection ID from the gift if available
    const collectionId = getGiftCollectionId(ctx);

    await ctx.telegram.sendMessage(
      chat_id,
      T(ctx, botMessages.processingGift.id)
    );

    const result = await depositGiftDirectlyForBot({
      gift: giftToTransfer,
      userTgId: userId,
      collectionId: collectionId as string,
    });

    log.info("Direct gift deposit result", {
      operation: "direct_gift_deposit_flow",
      chat_id,
      userId,
      success: result.success,
    });

    if (result.success) {
      await ctx.telegram.sendMessage(
        chat_id,
        T(ctx, botMessages.depositSuccess.id, { APP_NAME })
      );

      log.info("Direct gift deposit completed successfully", {
        operation: "direct_gift_deposit_flow",
        chat_id,
        userId,
      });

      return true;
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        T(ctx, botMessages.giftSentError.id)
      );

      log.warn("Direct gift deposit failed", {
        operation: "direct_gift_deposit_flow",
        chat_id,
        userId,
        result,
      });

      return false;
    }
  } catch (error) {
    log.error("Error in direct gift deposit flow", error, {
      operation: "direct_gift_deposit_flow",
      chat_id,
      userId,
      gift: giftToTransfer,
    });

    await ctx.telegram.sendMessage(
      chat_id,
      T(ctx, botMessages.giftSentError.id)
    );

    return false;
  }
};
