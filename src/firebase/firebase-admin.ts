import * as admin from "firebase-admin";
import { log } from "../utils/logger";
import {
  APP_CONFIG_COLLECTION,
  APP_USERS_COLLECTION,
  AppConfigEntity,
  BOT_SESSIONS_COLLECTION,
  BotSessionEntity,
  COLLECTION_NAME,
  CollectionEntity,
  GiftEntity,
  GIFTS_COLLECTION_NAME,
  OrderEntity,
  ORDERS_COLLECTION_NAME,
  UserEntity,
} from "../mikerudenko/marketplace-shared";

let firebaseApp: admin.app.App | null = null;

export function initializeFirebaseAdmin(): admin.app.App {
  if (firebaseApp) {
    return firebaseApp;
  }

  try {
    const nodeEnv = process.env.NODE_ENV ?? "development";

    let serviceAccountPath: string;
    if (nodeEnv === "production") {
      serviceAccountPath = "../keys/marketplace-prod-serviceAccountKey.json";
    } else {
      // Use development service account for local and development environments
      serviceAccountPath = "../keys/marketplace-dev-serviceAccountKey.json";
    }

    log.info("Initializing Firebase Admin SDK", {
      operation: "firebase_init",
      environment: nodeEnv,
      serviceAccountPath: serviceAccountPath.replace(process.cwd(), ""),
    });

    const serviceAccount = require(serviceAccountPath);

    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });

    // Configure Firestore settings
    const firestore = firebaseApp.firestore();
    firestore.settings({
      ignoreUndefinedProperties: true,
    });

    log.info("Firebase Admin SDK initialized successfully", {
      operation: "firebase_init",
      projectId: serviceAccount.project_id,
      environment: nodeEnv,
    });

    return firebaseApp;
  } catch (error) {
    log.error("Failed to initialize Firebase Admin SDK", error, {
      operation: "firebase_init",
    });
    throw new Error("Firebase initialization failed");
  }
}

export function getFirebaseApp(): admin.app.App {
  if (!firebaseApp) {
    return initializeFirebaseAdmin();
  }
  return firebaseApp;
}

export const db = getFirebaseApp().firestore();
export const auth = getFirebaseApp().auth();
export const DBBotSessionsCollection = db.collection(
  BOT_SESSIONS_COLLECTION
) as admin.firestore.CollectionReference<BotSessionEntity>;

export const DBGiftsCollection = db.collection(
  GIFTS_COLLECTION_NAME
) as admin.firestore.CollectionReference<GiftEntity>;

export const DBOrdersCollection = db.collection(
  ORDERS_COLLECTION_NAME
) as admin.firestore.CollectionReference<OrderEntity>;

export const DBAppConfigCollection = db.collection(
  APP_CONFIG_COLLECTION
) as admin.firestore.CollectionReference<AppConfigEntity>;

export const DBCollectionsCollection = db.collection(
  COLLECTION_NAME
) as admin.firestore.CollectionReference<CollectionEntity>;

export const DBUserCollection = db.collection(
  APP_USERS_COLLECTION
) as admin.firestore.CollectionReference<UserEntity>;
