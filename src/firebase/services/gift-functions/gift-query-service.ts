import { getFirestore } from "../../firebase-admin";
import {
  GiftEntity,
  GiftStatus,
  OrderEntity,
  OrderStatus,
  GIFTS_COLLECTION_NAME,
  ORDERS_COLLECTION_NAME,
} from "../../../miker<PERSON>nko/marketplace-shared";
import { validateTelegramId, validateGiftId } from "../shared/validation";
import { getUserById } from "../shared/user-lookup";
import { log } from "../../../utils/logger";

export interface GiftWithOrderInfo extends GiftEntity {
  relatedOrder?: {
    id: string;
    number: number;
    status: string;
    price: number;
  } | null;
}

export async function getUserGiftsAvailableForWithdrawalForBot(params: {
  tg_id: string;
}): Promise<GiftWithOrderInfo[]> {
  const { tg_id } = params;

  try {
    validateTelegramId(tg_id);

    const db = getFirestore();

    log.info("Getting user gifts available for withdrawal", {
      operation: "get_user_gifts_available_for_withdrawal_for_bot",
      tg_id,
    });

    // Get all gifts owned by the user with status 'deposited'
    const giftsQuery = await db
      .collection(GIFTS_COLLECTION_NAME)
      .where("owner_tg_id", "==", tg_id.toString())
      .where("status", "==", GiftStatus.DEPOSITED)
      .get();

    const gifts = giftsQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GiftEntity[];

    log.info("Found gifts for user", {
      tg_id,
      totalGifts: gifts.length,
      operation: "get_user_gifts_available_for_withdrawal_for_bot",
    });

    // Get all orders to check gift relationships
    const ordersQuery = await db.collection(ORDERS_COLLECTION_NAME).get();
    const orders = ordersQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    const availableGifts: GiftWithOrderInfo[] = [];

    for (const gift of gifts) {
      let isAvailable = false;
      let relatedOrder: GiftWithOrderInfo["relatedOrder"] = null;

      // Find orders related to this gift
      const relatedOrders = orders.filter((order) => order.giftId === gift.id);

      if (relatedOrders.length === 0) {
        // Gift not linked to any order - available for withdrawal
        log.debug("Gift not linked to any order", {
          giftId: gift.id,
          operation: "get_user_gifts_available_for_withdrawal_for_bot",
        });
        isAvailable = true;
      } else {
        // First, check for any exclusionary conditions (orders that prevent withdrawal)
        // Note: We need to check if user is seller by looking up user data
        let hasActiveOrder = false;
        for (const order of relatedOrders) {
          if (
            order.status === OrderStatus.ACTIVE ||
            order.status === OrderStatus.PAID ||
            order.status === OrderStatus.CREATED
          ) {
            // Check if user is the seller by looking up seller's telegram ID
            if (order.sellerId) {
              const seller = await getUserById(order.sellerId);
              if (seller?.tg_id === tg_id) {
                hasActiveOrder = true;
                break;
              }
            }
          }
        }

        if (hasActiveOrder) {
          log.debug("Gift has active order, not available for withdrawal", {
            giftId: gift.id,
            operation: "get_user_gifts_available_for_withdrawal_for_bot",
          });
          isAvailable = false;
        } else {
          // No exclusionary conditions, check for withdrawal-eligible conditions
          for (const order of relatedOrders) {
            // Gifts linked to orders with status 'gift_sent_to_relayer' where user is buyer
            if (
              order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
              order.buyerId
            ) {
              const buyer = await getUserById(order.buyerId);
              if (buyer?.tg_id === tg_id) {
                log.debug(
                  "Gift linked to gift_sent_to_relayer order where user is buyer",
                  {
                    giftId: gift.id,
                    orderId: order.id,
                    operation:
                      "get_user_gifts_available_for_withdrawal_for_bot",
                  }
                );
                isAvailable = true;
                relatedOrder = {
                  id: order.id!,
                  number: order.number,
                  status: order.status,
                  price: order.price,
                };
                break;
              }
            }

            // Gifts linked to orders with status 'cancelled' where user is seller
            if (order.status === OrderStatus.CANCELLED && order.sellerId) {
              const seller = await getUserById(order.sellerId);
              if (seller?.tg_id === tg_id) {
                log.debug(
                  "Gift linked to cancelled order where user is seller",
                  {
                    giftId: gift.id,
                    orderId: order.id,
                    operation:
                      "get_user_gifts_available_for_withdrawal_for_bot",
                  }
                );
                isAvailable = true;
                relatedOrder = {
                  id: order.id!,
                  number: order.number,
                  status: order.status,
                  price: order.price,
                };
                break;
              }
            }
          }
        }
      }

      if (isAvailable) {
        availableGifts.push({
          ...gift,
          relatedOrder,
        });
      }
    }

    log.info("Successfully retrieved user gifts available for withdrawal", {
      operation: "get_user_gifts_available_for_withdrawal_for_bot",
      tg_id,
      totalGifts: gifts.length,
      availableGifts: availableGifts.length,
    });

    return availableGifts;
  } catch (error) {
    log.error("Failed to get user gifts available for withdrawal", error, {
      operation: "get_user_gifts_available_for_withdrawal_for_bot",
      tg_id,
    });
    throw error;
  }
}

export async function getGiftByIdForBot(params: {
  giftId: string;
}): Promise<GiftEntity | null> {
  const { giftId } = params;

  try {
    validateGiftId(giftId);

    const db = getFirestore();
    const giftDoc = await db
      .collection(GIFTS_COLLECTION_NAME)
      .doc(giftId)
      .get();

    log.info("Gift retrieved by ID", {
      operation: "get_gift_by_id_for_bot",
      giftId,
      exists: giftDoc.exists,
    });

    if (!giftDoc.exists) {
      return null;
    }

    return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
  } catch (error) {
    log.error("Failed to get gift by ID", error, {
      operation: "get_gift_by_id_for_bot",
      giftId,
    });
    throw error;
  }
}
